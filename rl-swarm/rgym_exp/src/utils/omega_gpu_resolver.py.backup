import random

import torch
from omegaconf import OmegaConf


def get_gpu_vram():
    """Returns the total VRAM of the first available GPU in GiB."""
    # === RTX3090_FAKE_PATCH_START ===
    # 伪装成RTX 3090 GPU (24GB VRAM)
    if os.getenv('FAKE_RTX3090_GPU', 'false').lower() == 'true':
        print("[GPU_FAKE] 🎭 伪装成RTX 3090 GPU (24GB VRAM)")
        return 24.0
    # === RTX3090_FAKE_PATCH_END ===

    if not torch.cuda.is_available():
        return 0

    total_memory = torch.cuda.get_device_properties(0).total_memory
    return total_memory / (1024**3)  # Convert bytes to GiB


def gpu_model_choice_resolver(large_model_pool, small_model_pool):
    """Selects a model from the large or small pool based on VRAM."""
    vram = get_gpu_vram()

    # === RTX3090_FAKE_PATCH_START ===
    # RTX 3090 24GB适合大模型池
    if os.getenv('FAKE_RTX3090_GPU', 'false').lower() == 'true':
        print(f"[GPU_FAKE] 🎯 检测到{vram}GB VRAM，选择大模型池")
        model_pool = large_model_pool
        selected_model = random.choice(model_pool)
        print(f"[GPU_FAKE] 🤖 选择模型: {selected_model}")
        return selected_model
    # === RTX3090_FAKE_PATCH_END ===

    if vram >= 20:  # RTX 3090门槛降低到20GB
        model_pool = large_model_pool
    else:
        model_pool = small_model_pool
    return random.choice(model_pool)


# 添加os导入
import os

OmegaConf.register_new_resolver("gpu_model_choice", gpu_model_choice_resolver)
