#!/usr/bin/env python3
"""
完整RTX 3090伪装系统
伪装所有可能被检测的硬件信息
"""

import os
import sys
import psutil
import platform
from unittest.mock import patch, MagicMock

class CompleteRTX3090Faker:
    """完整RTX 3090伪装器"""

    def __init__(self):
        self.original_functions = {}
        self.is_active = False

    def fake_torch_cuda_functions(self):
        """伪装PyTorch CUDA函数"""
        try:
            import torch

            # 备份原始函数
            if hasattr(torch.cuda, 'get_device_properties'):
                self.original_functions['get_device_properties'] = torch.cuda.get_device_properties
            if hasattr(torch.cuda, 'memory_allocated'):
                self.original_functions['memory_allocated'] = torch.cuda.memory_allocated
            if hasattr(torch.cuda, 'memory_reserved'):
                self.original_functions['memory_reserved'] = torch.cuda.memory_reserved
            if hasattr(torch.cuda, 'max_memory_allocated'):
                self.original_functions['max_memory_allocated'] = torch.cuda.max_memory_allocated
            if hasattr(torch.cuda, 'is_available'):
                self.original_functions['is_available'] = torch.cuda.is_available
            if hasattr(torch.cuda, 'device_count'):
                self.original_functions['device_count'] = torch.cuda.device_count

            # 创建伪装的设备属性
            class FakeDeviceProperties:
                def __init__(self):
                    self.name = "NVIDIA GeForce RTX 3090"
                    self.major = 8
                    self.minor = 6  # RTX 3090计算能力8.6
                    self.total_memory = 25769803776  # 24GB in bytes
                    self.multi_processor_count = 82  # RTX 3090有82个SM
                    self.max_threads_per_multi_processor = 2048
                    self.max_shared_memory_per_block = 49152  # RTX 3090共享内存
                    self.warp_size = 32
                    
            def fake_get_device_properties(device=0):
                print(f"[RTX3090_FAKE] 🎭 伪装GPU设备属性: GeForce RTX 3090")
                return FakeDeviceProperties()

            def fake_memory_allocated(device=0):
                # 返回合理的RTX 3090内存使用量 (约4GB)
                fake_memory = 4 * 1024 * 1024 * 1024  # 4GB
                print(f"[RTX3090_FAKE] 🎭 伪装GPU内存分配: {fake_memory / 1024**3:.1f}GB")
                return fake_memory

            def fake_memory_reserved(device=0):
                # 返回合理的RTX 3090内存预留量 (约5GB)
                fake_memory = 5 * 1024 * 1024 * 1024  # 5GB
                print(f"[RTX3090_FAKE] 🎭 伪装GPU内存预留: {fake_memory / 1024**3:.1f}GB")
                return fake_memory

            def fake_max_memory_allocated(device=0):
                # 返回合理的RTX 3090最大内存使用量
                fake_memory = 8 * 1024 * 1024 * 1024  # 8GB
                return fake_memory

            def fake_is_available():
                print(f"[RTX3090_FAKE] 🎭 伪装CUDA可用性: True")
                return True

            def fake_device_count():
                print(f"[RTX3090_FAKE] 🎭 伪装GPU数量: 1")
                return 1
            
            # 替换函数
            torch.cuda.get_device_properties = fake_get_device_properties
            torch.cuda.memory_allocated = fake_memory_allocated
            torch.cuda.memory_reserved = fake_memory_reserved
            torch.cuda.max_memory_allocated = fake_max_memory_allocated
            torch.cuda.is_available = fake_is_available
            torch.cuda.device_count = fake_device_count

            print("[RTX3090_FAKE] ✅ PyTorch CUDA函数伪装完成")

        except ImportError:
            print("[RTX3090_FAKE] ⚠️  PyTorch未安装，跳过CUDA伪装")
    
    def fake_psutil_functions(self):
        """伪装psutil系统信息函数"""
        
        # 备份原始函数
        self.original_functions['virtual_memory'] = psutil.virtual_memory
        self.original_functions['cpu_count'] = psutil.cpu_count
        
        def fake_virtual_memory():
            """伪装系统内存为RTX 3090工作站级别"""
            # 创建伪装的内存信息 (64GB高端工作站)
            class FakeVirtualMemory:
                def __init__(self):
                    self.total = 64 * 1024 * 1024 * 1024  # 64GB
                    self.available = 48 * 1024 * 1024 * 1024  # 48GB可用
                    self.used = 16 * 1024 * 1024 * 1024  # 16GB已用
                    self.percent = (self.used / self.total) * 100

            fake_mem = FakeVirtualMemory()
            print(f"[RTX3090_FAKE] 🎭 伪装系统内存: {fake_mem.total / 1024**3:.0f}GB总量")
            return fake_mem

        def fake_cpu_count(logical=True):
            """伪装CPU核心数为RTX 3090工作站级别"""
            if logical:
                count = 32  # 16核心32线程 (i9-12900K)
            else:
                count = 16   # 16物理核心
            print(f"[RTX3090_FAKE] 🎭 伪装CPU核心数: {count} ({'逻辑' if logical else '物理'})")
            return count
        
        # 替换函数
        psutil.virtual_memory = fake_virtual_memory
        psutil.cpu_count = fake_cpu_count

        print("[RTX3090_FAKE] ✅ psutil系统信息伪装完成")
    
    def fake_platform_functions(self):
        """伪装platform系统信息"""
        
        # 备份原始函数
        self.original_functions['platform_processor'] = platform.processor
        self.original_functions['platform_machine'] = platform.machine
        
        def fake_processor():
            processor_name = "Intel(R) Core(TM) i9-12900K"
            print(f"[RTX3090_FAKE] 🎭 伪装处理器: {processor_name}")
            return processor_name

        def fake_machine():
            machine_type = "x86_64"
            print(f"[RTX3090_FAKE] 🎭 伪装机器类型: {machine_type}")
            return machine_type

        # 替换函数
        platform.processor = fake_processor
        platform.machine = fake_machine

        print("[RTX3090_FAKE] ✅ platform系统信息伪装完成")
    
    def fake_system_info_generation(self):
        """伪装系统信息生成"""
        try:
            from genrl.logging_utils.system_utils import get_system_info
            
            # 备份原始函数
            self.original_functions['get_system_info'] = get_system_info
            
            def fake_get_system_info():
                """生成伪装的RTX 4090工作站系统信息"""
                lines = ["\n"]
                lines.append("[---------] SYSTEM INFO [---------]")
                lines.append("")
                lines.append("Python Version:")
                lines.append(f"  {sys.version}")

                lines.append("\nPlatform Information:")
                lines.append(f"  System: Linux")
                lines.append(f"  Release: 5.15.0-72-generic")
                lines.append(f"  Version: #79-Ubuntu SMP Wed Apr 19 08:22:18 UTC 2023")
                lines.append(f"  Machine: x86_64")
                lines.append(f"  Processor: Intel(R) Core(TM) i9-13900K")

                lines.append("\nCPU Information:")
                lines.append(f"  Physical cores: 16")
                lines.append(f"  Total cores: 32")
                lines.append(f"  Max Frequency: 5800.00 Mhz")
                lines.append(f"  Current Frequency: 3000.00 Mhz")

                lines.append("\nMemory Information:")
                lines.append(f"  Total: 64.00 GB")
                lines.append(f"  Available: 48.00 GB")
                lines.append(f"  Used: 16.00 GB")

                lines.append("\nGPU Information:")
                lines.append(f"  Device: NVIDIA GeForce RTX 4090")
                lines.append(f"  Memory: 24.00 GB")
                lines.append(f"  Compute Capability: 8.9")
                
                lines.append("\n[---------] END SYSTEM INFO [---------]")
                
                fake_info = "\n".join(lines)
                print("[RTX3090_FAKE] 🎭 生成伪装系统信息")
                return fake_info

            # 替换函数
            import genrl.logging_utils.system_utils
            genrl.logging_utils.system_utils.get_system_info = fake_get_system_info

            print("[RTX3090_FAKE] ✅ 系统信息生成伪装完成")

        except ImportError:
            print("[RTX3090_FAKE] ⚠️  genrl未安装，跳过系统信息伪装")
    
    def apply_complete_fake(self):
        """应用完整RTX 3090伪装"""
        if self.is_active:
            print("[RTX3090_FAKE] ⚠️  RTX 3090伪装已激活")
            return

        print("[RTX3090_FAKE] 🎭 开始应用完整RTX 3090伪装...")

        # 设置环境变量
        os.environ['FAKE_RTX3090_GPU'] = 'true'
        os.environ['CUDA_VISIBLE_DEVICES'] = '0'

        # 应用各种伪装
        self.fake_torch_cuda_functions()
        self.fake_psutil_functions()
        self.fake_platform_functions()
        self.fake_system_info_generation()

        self.is_active = True

        print("[RTX3090_FAKE] ✅ 完整RTX 3090伪装已激活")
        print("[RTX3090_FAKE] 🖥️  伪装配置:")
        print("[RTX3090_FAKE]    GPU: NVIDIA GeForce RTX 3090 (24GB VRAM)")
        print("[RTX3090_FAKE]    CPU: Intel i9-12900K 16-Core (32线程)")
        print("[RTX3090_FAKE]    RAM: 64GB DDR5")
        print("[RTX3090_FAKE]    系统: Ubuntu 22.04 LTS")
    
    def restore_original(self):
        """恢复原始函数"""
        if not self.is_active:
            print("[RTX3090_FAKE] ⚠️  RTX 3090伪装未激活")
            return

        print("[RTX3090_FAKE] 🔄 恢复原始系统函数...")

        try:
            # 恢复PyTorch函数
            import torch
            for func_name, original_func in self.original_functions.items():
                if func_name.startswith('torch_'):
                    setattr(torch.cuda, func_name.replace('torch_', ''), original_func)
        except ImportError:
            pass

        # 恢复psutil函数
        if 'virtual_memory' in self.original_functions:
            psutil.virtual_memory = self.original_functions['virtual_memory']
        if 'cpu_count' in self.original_functions:
            psutil.cpu_count = self.original_functions['cpu_count']

        # 恢复platform函数
        if 'platform_processor' in self.original_functions:
            platform.processor = self.original_functions['platform_processor']
        if 'platform_machine' in self.original_functions:
            platform.machine = self.original_functions['platform_machine']

        # 恢复系统信息函数
        try:
            if 'get_system_info' in self.original_functions:
                import genrl.logging_utils.system_utils
                genrl.logging_utils.system_utils.get_system_info = self.original_functions['get_system_info']
        except ImportError:
            pass

        # 清除环境变量
        if 'FAKE_RTX3090_GPU' in os.environ:
            del os.environ['FAKE_RTX3090_GPU']

        self.is_active = False
        print("[RTX3090_FAKE] ✅ 原始系统函数已恢复")

# 全局伪装器实例
_complete_faker = None

def enable_complete_rtx3090_fake():
    """启用完整RTX 3090伪装"""
    global _complete_faker
    if _complete_faker is None:
        _complete_faker = CompleteRTX3090Faker()
    _complete_faker.apply_complete_fake()
    return _complete_faker

def disable_complete_rtx3090_fake():
    """禁用完整RTX 3090伪装"""
    global _complete_faker
    if _complete_faker:
        _complete_faker.restore_original()

def get_complete_faker():
    """获取完整伪装器实例"""
    return _complete_faker

if __name__ == "__main__":
    # 测试完整RTX 3090伪装
    print("🧪 测试完整RTX 3090伪装功能")

    faker = enable_complete_rtx3090_fake()

    # 测试各种检测
    try:
        import torch
        if torch.cuda.is_available():
            props = torch.cuda.get_device_properties(0)
            print(f"✅ GPU: {props.name}")
            print(f"✅ VRAM: {props.total_memory / 1024**3:.1f}GB")
            print(f"✅ 已分配内存: {torch.cuda.memory_allocated() / 1024**3:.1f}GB")
    except ImportError:
        print("⚠️  PyTorch未安装")

    # 测试系统信息
    vm = psutil.virtual_memory()
    print(f"✅ 系统内存: {vm.total / 1024**3:.0f}GB")
    print(f"✅ CPU核心: {psutil.cpu_count()} 逻辑核心")
    print(f"✅ 处理器: {platform.processor()}")

    disable_complete_rtx3090_fake()
